“句之炼金” (Sentence Alchemist) App - 产品设计文档 (v1.0)
1. 产品愿景与核心理念
1.1 愿景 (Vision)
打造一款让语言学习者忘掉孤立单词、拥抱完整语境的句子学习工具，通过高效、专注的训练，帮助用户将“死”的知识转化为“活”的语言能力。

1.2 核心理念 (Core Philosophy)
句子优先 (Sentence First): 学习的基本单位是句子，而非单词。

语境为王 (Context is King): 句子的含义只有在特定语境中才能被完全理解和掌握。

理解驱动 (Comprehension-Driven): 强调对句子整体意思的理解，而非对单个单词的机械记忆。

专注高效 (Focused & Efficient): 摒弃干扰，提供一个纯粹的句子-意思匹配训练环境。

1.3 目标用户 (Target Audience)
英语初学者/中级学习者： 已经认识一些单词，但无法将它们组织成句子进行有效沟通的人。

传统学习方法受挫者： 对背单词书感到厌倦和低效，寻求更实用学习方式的用户。

有明确沟通需求者： 像你一样，为了旅游、工作等实际目的，需要快速提升口语和听力理解能力的用户。

2. 核心功能设计 (Core Features)
2.1 核心训练：句子匹配 (Sentence Matching)
这是App的核心玩法和学习闭环。

界面展示:

问题区域 (Question Area): 屏幕上方突出显示一个完整的英文句子。

选项区域 (Options Area): 屏幕下方提供四个中文选项。其中一个是正确翻译，另外三个是干扰项。

交互流程:

用户阅读英文句子。

用户在四个中文选项中选择他认为正确的翻译。

即时反馈:

选择正确: 选项变绿，播放清脆的成功音效。句子被标记为“初步掌握”，并自动进入下一个句子的学习。

选择错误: 选项变红，播放提示音效。正确的中文答案会高亮显示，并短暂驻留（例如2-3秒），加深用户印象。该句子会被标记为“待复习”。

干扰项设计 (Crucial for effectiveness):

干扰项不应是随机的中文句子。

应包含与原句中某个或某几个单词相关的“错误”翻译，以此来考验用户是否真正理解了整个句子的结构和语境。

例:

英文句: Can I have a window seat, please?

正确选项: 可以给我一个靠窗的座位吗？

干扰项1: 我能看见一个窗户座位吗？ (对 have 的错误理解)

干扰项2: 我有一个靠窗的座位。 (陈述句，而非疑问句)

干扰项3: 请坐在窗户旁边。 (完全无关，但包含“窗户”和“座位”)

2.2 句子库 (Sentence Library)
这是App的内容来源，也是用户学习的“弹药库”。

分类: 句子应按场景和难度进行分类。

场景分类: 机场、餐厅、酒店、购物、工作会议、日常闲聊等。

难度分类: 初级 (短句、简单时态)、中级、高级。

内容来源:

内置内容: App预置大量高质量、地道的句子。

用户创建 (未来功能): 允许用户创建自己的句子卡片，增加个性化。

句子详情页: 点击一个句子，可以进入详情页查看更多信息，如：

标准美式/英式发音（可切换）。

句子中核心单词或短语的简单讲解。

相似句型举例。

2.3 智能复习系统 (Smart Review System)
基于“艾宾浩斯遗忘曲线”理论，确保学习效果。

“待复习”列表: 所有答错的句子，或用户手动标记需要复习的句子，都会进入这个列表。

间隔重复算法:

一个新句子，答对后，可能10分钟后再次出现。

再次答对，可能1天后出现。

再次答对，可能3天、一周、一个月后出现...

一旦答错，复习周期重置。

复习模式: 用户可以随时进入“复习模式”，专门练习“待复习”列表中的句子。

2.4 个人进度与统计 (User Progress & Stats)
给予用户正向激励和成就感。

数据面板:

今日学习句子数 / 复习句子数。

累计掌握句子总数。

学习日历（打卡功能），显示连续学习天数。

成就徽章:

完成“机场”场景所有句子，获得“旅行家”徽章。

累计掌握500个句子，获得“句子大师”徽章。

3. 用户流程图 (User Flow)
新用户打开App -> 简单引导页 (介绍核心理念) -> 选择一个感兴趣的场景 (如“机场”)" -> 进入“句子匹配”训练 -> (答对/答错，即时反馈) -> 完成一组训练 (如10个句子) -> 显示本组小结 (正确率、新掌握的句子) -> 可选择“继续学习新句”或“进入复习模式” -> 返回主页查看个人进度

4. UI/UX 设计概念 (UI/UX Concepts)
风格: 简洁、干净、专注。避免不必要的装饰和动画，让用户聚焦于句子本身。

色彩: 使用清晰的色彩体系来表示状态（如绿色代表正确，红色代表错误，蓝色代表中性）。

字体: 选用清晰易读的英文字体和中文字体。

交互: 点击操作要灵敏，反馈要及时。

5. 未来发展路线图 (Future Roadmap)
V1.1 - 听力模式: 隐藏英文句子，只播放音频，让用户根据听力选择正确的中文意思。

V1.2 - 口语模式: 用户选择正确的中文意思后，鼓励用户跟读英文句子，App通过语音识别技术进行打分。

V2.0 - 用户自定义内容 (UGC): 允许用户创建和分享自己的句子列表，形成社区。

V2.1 - 社交功能: 可以和朋友PK，比比谁在规定时间内答对的句子更多。

6. 技术选型与开发策略 (Technology & Development Strategy)
6.1 开发策略: MVP (Minimum Viable Product - 最小可行产品) 优先
我们的第一步是构建一个Web网页版应用。这是最明智的起步方式，原因如下：

速度快，成本低: 无需适配不同手机系统，一套代码即可在所有浏览器运行。

快速验证: 能以最快速度将核心想法（句子匹配学习法）交给真实用户检验，收集反馈。

迭代灵活: 根据用户反馈，可以非常方便地修改和部署新版本。

6.2 技术栈 (Technology Stack)
前端 (Frontend): React.js。它是构建现代化、交互式单页应用的首选框架。其组件化的思想非常适合开发我们的App（如问题组件、选项组件等），生态强大，性能优异。

后端 (Backend): Node.js + Firestore。在项目初期，我们可以将句子库直接内置于前端。当需要存储用户进度、自定义列表等功能时，Node.js能让我们用同一种语言(JavaScript)开发后端，而Google的Firestore数据库则能提供实时、可扩展的数据存储服务，极大简化开发。

6.3 协作模式 (Collaboration Model): 掌舵者 + AI开发者
我们采用一种高效、前沿的协作模式来推进项目：

你的角色 (The Helmsman - 掌舵者):

定义愿景: 提出产品想法和功能需求。

做出决策: 在关键节点选择技术方向和功能优先级。

测试反馈: 检验生成的功能是否符合预期，并提出修改意见。

我的角色 (The AI Developer - AI开发者):

理解需求: 分析你的需求文档和指令。

生成代码: 编写高质量、可运行的前端和后端代码。

修复迭代: 根据你的反馈，快速修复问题和迭代新功能。